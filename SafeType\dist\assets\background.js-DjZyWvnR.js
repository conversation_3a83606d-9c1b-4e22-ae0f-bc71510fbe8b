async function o(e){const t=(await chrome.storage.sync.get(["geminiApiKey"])).geminiApiKey;if(!t)return"Please set your Gemini API key in the extension options.";const i={contents:[{parts:[{text:`Rewrite the following in a confident and calming tone:

"${e}"`}]}]};return(await(await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})).json())?.candidates?.[0]?.content?.parts?.[0]?.text||"Rewrite failed"}chrome.runtime.onInstalled.addListener(()=>{chrome.contextMenus.create({id:"rewriteText",title:"Rewrite with SafeType",contexts:["selection"]})});chrome.contextMenus.onClicked.addListener((e,n)=>{e.menuItemId==="rewriteText"&&e.selectionText&&o(e.selectionText).then(t=>{chrome.scripting.executeScript({target:{tabId:n.id},func:r,args:[t]})})});function r(e){const n=window.getSelection();if(n.rangeCount>0){const t=n.getRangeAt(0);t.deleteContents(),t.insertNode(document.createTextNode(e)),n.removeAllRanges()}}chrome.runtime.onMessage.addListener((e,n,t)=>{if(e.type==="REWRITE_TEXT")return o(e.payload).then(i=>{t({rewrittenText:i})}),!0});
