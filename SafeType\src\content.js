console.log("SafeType content script loaded");

let currentActiveElement = null;
let confidenceCheckTimeout = null;
let confidenceNotification = null;

// Monitor text input in various elements
function monitorTextInput() {
    // Target common input elements
    const inputSelectors = [
        'textarea',
        'input[type="text"]',
        'input[type="email"]',
        '[contenteditable="true"]',
        '[role="textbox"]',
        '.editable', // Common class for editable elements
        '[data-testid*="compose"]', // Gmail compose
        '[data-testid*="message"]', // Chat apps
    ];

    inputSelectors.forEach(selector => {
        document.querySelectorAll(selector).forEach(element => {
            if (!element.hasAttribute('data-safetype-monitored')) {
                element.setAttribute('data-safetype-monitored', 'true');

                element.addEventListener('input', handleTextInput);
                element.addEventListener('focus', handleElementFocus);
                element.addEventListener('blur', handleElementBlur);
            }
        });
    });
}

function handleElementFocus(event) {
    currentActiveElement = event.target;
}

function handleElementBlur(event) {
    if (currentActiveElement === event.target) {
        currentActiveElement = null;
        hideConfidenceNotification();
    }
}

function handleTextInput(event) {
    const element = event.target;
    const text = getElementText(element);

    // Clear previous timeout
    if (confidenceCheckTimeout) {
        clearTimeout(confidenceCheckTimeout);
    }

    // Only analyze if text is substantial (more than 10 characters)
    if (text.length > 10) {
        confidenceCheckTimeout = setTimeout(() => {
            analyzeTextConfidence(text, element);
        }, 2000); // Wait 2 seconds after user stops typing
    } else {
        hideConfidenceNotification();
    }
}

function getElementText(element) {
    if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
        return element.value;
    } else if (element.contentEditable === 'true') {
        return element.innerText || element.textContent;
    }
    return '';
}

async function analyzeTextConfidence(text, element) {
    try {
        const response = await chrome.runtime.sendMessage({
            type: 'ANALYZE_CONFIDENCE',
            payload: text
        });

        if (response.success && response.analysis) {
            const { confidence_score, needs_rewriting, explanation, suggested_improvements } = response.analysis;

            // Show notification if confidence is low (score < 6)
            if (confidence_score < 6 && needs_rewriting) {
                showConfidenceNotification(element, {
                    score: confidence_score,
                    explanation,
                    improvements: suggested_improvements,
                    originalText: text
                });
            } else {
                hideConfidenceNotification();
            }
        }
    } catch (error) {
        console.error('SafeType: Error analyzing confidence:', error);
    }
}

// Add context menu functionality for text selection
document.addEventListener('mouseup', function() {
    const selectedText = window.getSelection().toString().trim();
    if (selectedText.length > 0) {
        chrome.runtime.sendMessage({
            type: 'TEXT_SELECTED',
            payload: selectedText
        });
    }
});

// Listen for messages from popup or background
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'GET_SELECTED_TEXT') {
        const selectedText = window.getSelection().toString().trim();
        sendResponse({ selectedText: selectedText });
    }
    return true;
});

function showConfidenceNotification(element, data) {
    hideConfidenceNotification(); // Remove any existing notification

    const notification = document.createElement('div');
    notification.id = 'safetype-confidence-notification';
    notification.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                <strong style="color: #d97706;">Low Confidence Detected</strong>
            </div>

            <div style="margin-bottom: 12px; color: #374151;">
                <div><strong>Confidence Score:</strong> ${data.score}/10</div>
                <div style="margin-top: 4px;"><strong>Issue:</strong> ${data.explanation}</div>
                <div style="margin-top: 4px;"><strong>Suggestion:</strong> ${data.improvements}</div>
            </div>

            <div style="margin-bottom: 12px; font-weight: 500; color: #374151;">
                Would you like me to rewrite this text with more confidence?
            </div>

            <div style="display: flex; gap: 8px;">
                <button id="safetype-rewrite-yes" style="
                    background: #4F46E5;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">Yes, Rewrite</button>

                <button id="safetype-rewrite-no" style="
                    background: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">No, Keep As Is</button>
            </div>
        </div>
    `;

    document.body.appendChild(notification);
    confidenceNotification = notification;

    // Add event listeners
    document.getElementById('safetype-rewrite-yes').addEventListener('click', () => {
        rewriteText(element, data.originalText);
    });

    document.getElementById('safetype-rewrite-no').addEventListener('click', () => {
        hideConfidenceNotification();
    });

    // Auto-hide after 15 seconds
    setTimeout(() => {
        hideConfidenceNotification();
    }, 15000);
}

function hideConfidenceNotification() {
    if (confidenceNotification) {
        confidenceNotification.remove();
        confidenceNotification = null;
    }
}

async function rewriteText(element, originalText) {
    // Show loading state
    const yesButton = document.getElementById('safetype-rewrite-yes');
    if (yesButton) {
        yesButton.textContent = 'Rewriting...';
        yesButton.disabled = true;
    }

    try {
        const response = await chrome.runtime.sendMessage({
            type: 'REWRITE_TEXT',
            payload: originalText
        });

        if (response && response.rewrittenText) {
            // Replace text in the element
            setElementText(element, response.rewrittenText);

            // Show success message briefly
            if (confidenceNotification) {
                confidenceNotification.innerHTML = `
                    <div style="
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #10b981;
                        color: white;
                        border-radius: 12px;
                        padding: 16px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        z-index: 10000;
                        max-width: 350px;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        font-size: 14px;
                        text-align: center;
                    ">
                        ✅ Text rewritten with more confidence!
                    </div>
                `;

                setTimeout(() => {
                    hideConfidenceNotification();
                }, 3000);
            }
        }
    } catch (error) {
        console.error('SafeType: Error rewriting text:', error);
        hideConfidenceNotification();
    }
}

function setElementText(element, text) {
    if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
        element.value = text;
        element.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (element.contentEditable === 'true') {
        element.innerText = text;
        element.dispatchEvent(new Event('input', { bubbles: true }));
    }
}

// Initialize monitoring
monitorTextInput();

// Re-monitor when new elements are added to the page
const observer = new MutationObserver(() => {
    monitorTextInput();
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});
