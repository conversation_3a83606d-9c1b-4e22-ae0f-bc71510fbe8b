import"./modulepreload-polyfill-B5Qt9EMX.js";document.addEventListener("DOMContentLoaded",function(){const o=document.getElementById("inputText"),e=document.getElementById("rewriteBtn"),n=document.getElementById("output");e.addEventListener("click",async function(){const t=o.value.trim();if(!t){n.textContent="Please enter some text to rewrite.";return}e.disabled=!0,e.textContent="Rewriting...",n.innerHTML='<span class="loading">Processing your text...</span>';try{const r=await chrome.runtime.sendMessage({type:"REWRITE_TEXT",payload:t});r&&r.rewrittenText?n.textContent=r.rewrittenText:n.textContent="Sorry, there was an error rewriting your text. Please try again."}catch(r){console.error("Error:",r),n.textContent="Sorry, there was an error rewriting your text. Please try again."}finally{e.disabled=!1,e.textContent="Rewrite with Confidence"}}),o.addEventListener("keydown",function(t){(t.ctrlKey||t.metaKey)&&t.key==="Enter"&&e.click()})});
