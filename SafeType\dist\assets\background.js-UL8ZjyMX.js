async function l(e){try{const t=(await chrome.storage.sync.get(["geminiApiKey"])).geminiApiKey;if(!t)return{error:"Please set your Gemini API key in the extension options."};const o={contents:[{parts:[{text:`Analyze the confidence level of this text on a scale of 1-10 (where 1 is very uncertain/hesitant and 10 is very confident/assertive). Also provide a brief explanation and suggest if it needs rewriting.

Text: "${e}"

Respond in this exact JSON format:
{
  "confidence_score": [number 1-10],
  "explanation": "[brief explanation of why this score]",
  "needs_rewriting": [true/false],
  "suggested_improvements": "[what could be improved]"
}`}]}]},n=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok)return{error:`API Error (${n.status})`};const a=(await n.json())?.candidates?.[0]?.content?.parts?.[0]?.text;if(!a)return{error:"No response from AI"};try{const i=a.match(/\{[\s\S]*\}/);return i?{success:!0,analysis:JSON.parse(i[0])}:{error:"Could not parse AI response"}}catch{return{error:"Invalid response format"}}}catch(r){return{error:r.message}}}async function c(e){try{const t=(await chrome.storage.sync.get(["geminiApiKey"])).geminiApiKey;if(console.log("API Key exists:",!!t),!t)return"Please set your Gemini API key in the extension options.";if(!e||e.trim().length===0)return"Please provide some text to rewrite.";const o={contents:[{parts:[{text:`Rewrite the following text in a more confident and calming tone. Keep the same meaning but make it sound more assured and positive:

"${e}"`}]}]};console.log("Making API request to Gemini...");const n=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(console.log("Response status:",n.status),!n.ok){const i=await n.json();return console.error("API Error:",i),n.status===400?"Invalid API request. Please check your API key.":n.status===403?"API key is invalid or doesn't have permission. Please check your Gemini API key.":n.status===429?"Too many requests. Please wait a moment and try again.":`API Error (${n.status}): ${i.error?.message||"Unknown error"}`}const s=await n.json();if(console.log("API Response:",s),s.error)return console.error("Gemini API Error:",s.error),`Error: ${s.error.message}`;const a=s?.candidates?.[0]?.content?.parts?.[0]?.text;return a?a.trim():(console.error("No text in response:",s),"No response generated. The content might have been filtered.")}catch(r){return console.error("Rewrite function error:",r),`Error: ${r.message}`}}chrome.runtime.onInstalled.addListener(()=>{chrome.contextMenus.create({id:"rewriteText",title:"Rewrite with SafeType",contexts:["selection"]})});chrome.contextMenus.onClicked.addListener((e,r)=>{e.menuItemId==="rewriteText"&&e.selectionText&&c(e.selectionText).then(t=>{chrome.scripting.executeScript({target:{tabId:r.id},func:d,args:[t]})})});function d(e){const r=window.getSelection();if(r.rangeCount>0){const t=r.getRangeAt(0);t.deleteContents(),t.insertNode(document.createTextNode(e)),r.removeAllRanges()}}chrome.runtime.onMessage.addListener((e,r,t)=>{if(e.type==="REWRITE_TEXT")return c(e.payload).then(o=>{t({rewrittenText:o})}),!0;if(e.type==="ANALYZE_CONFIDENCE")return l(e.payload).then(o=>{t(o)}),!0});
