# SafeType Chrome Extension

A confidence-boosting writing assistant that automatically analyzes your text and helps you write with more confidence using Google's Gemini AI.

## ✨ Features

- **🔍 Real-time Confidence Analysis**: Automatically detects when your text lacks confidence while typing
- **⚡ Smart Notifications**: Shows helpful suggestions when text could be more confident  
- **✍️ One-Click Rewriting**: Yes/No buttons to instantly improve your text
- **🌐 Universal Compatibility**: Works in Gmail, chat apps, social media, and any text input
- **📊 Confidence Scoring**: Get detailed analysis with scores from 1-10
- **🎯 Popup Interface**: Analyze and rewrite text directly from the extension popup
- **🖱️ Context Menu**: Right-click on selected text to rewrite it instantly
- **🔒 Secure API Key Storage**: Store your Gemini API key securely in Chrome storage

## 🚀 How It Works

1. **Type anywhere** - Gmail, WhatsApp Web, Twitter, LinkedIn, etc.
2. **Get instant feedback** - Extension analyzes confidence level as you type
3. **See smart notifications** - Get alerts when text could be more confident
4. **One-click improvement** - Click "Yes" to automatically rewrite with confidence
5. **Keep or discard** - Choose to keep the original or use the improved version

## 📋 Setup Instructions

### 1. Get a Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a free API key
3. Copy the API key for later use

### 2. Build the Extension
```bash
npm install
npm run build
```

### 3. Load the Extension in Chrome
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked"
4. Select the `dist` folder that was created after building

### 4. Configure the Extension
1. Right-click the SafeType extension icon
2. Select "Options"
3. Enter your Gemini API key
4. Click "Save Settings"

## 💡 Usage Examples

### Real-time Analysis While Typing
- Type in any text field (Gmail, Twitter, LinkedIn, etc.)
- Extension automatically analyzes confidence after 2 seconds
- Get notifications when text could be more confident
- Click "Yes, Rewrite" to improve instantly

### Manual Analysis in Popup
1. Click the SafeType extension icon
2. Enter or paste your text
3. Click "Check Confidence" to analyze
4. Click "Rewrite Text" to improve

### Context Menu
1. Select any text on a webpage
2. Right-click and select "Rewrite with SafeType"
3. Text gets replaced with more confident version

## 🎯 Perfect For

- **Email writing** (Gmail, Outlook)
- **Social media posts** (Twitter, LinkedIn, Facebook)
- **Chat applications** (WhatsApp Web, Slack, Discord)
- **Professional communication**
- **Academic writing**
- **Any text input on the web**
