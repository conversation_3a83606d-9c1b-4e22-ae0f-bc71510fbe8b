import"./modulepreload-polyfill-B5Qt9EMX.js";document.addEventListener("DOMContentLoaded",function(){const o=document.getElementById("optionsForm"),s=document.getElementById("apiKey"),t=document.getElementById("status");chrome.storage.sync.get(["geminiApiKey"],function(e){e.geminiApiKey&&(s.value=e.geminiApiKey)}),o.addEventListener("submit",function(e){e.preventDefault();const n=s.value.trim();if(!n){i("Please enter an API key.","error");return}chrome.storage.sync.set({geminiApiKey:n},function(){i("Settings saved successfully!","success")})});function i(e,n){t.textContent=e,t.className=`status ${n}`,t.classList.remove("hidden"),setTimeout(()=>{t.classList.add("hidden")},3e3)}});
