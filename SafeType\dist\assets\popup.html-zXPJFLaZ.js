import"./modulepreload-polyfill-B5Qt9EMX.js";document.addEventListener("DOMContentLoaded",function(){const i=document.getElementById("inputText"),o=document.getElementById("rewriteBtn"),r=document.getElementById("analyzeBtn"),t=document.getElementById("output");o.addEventListener("click",async function(){const n=i.value.trim();if(!n){t.textContent="Please enter some text to rewrite.";return}o.disabled=!0,o.textContent="Rewriting...",t.innerHTML='<span class="loading">Processing your text...</span>';try{const e=await chrome.runtime.sendMessage({type:"REWRITE_TEXT",payload:n});console.log("Response from background:",e),e&&e.rewrittenText?e.rewrittenText.startsWith("Error:")||e.rewrittenText.startsWith("Please set your")||e.rewrittenText.startsWith("API Error")||e.rewrittenText.startsWith("Invalid")||e.rewrittenText.startsWith("Too many")?t.innerHTML=`<span style="color: #dc2626;">${e.rewrittenText}</span>`:t.textContent=e.rewrittenText:t.innerHTML='<span style="color: #dc2626;">No response received. Please check your API key and try again.</span>'}catch(e){console.error("Popup Error:",e),t.innerHTML=`<span style="color: #dc2626;">Connection error: ${e.message}</span>`}finally{o.disabled=!1,o.textContent="Rewrite Text"}}),r.addEventListener("click",async function(){const n=i.value.trim();if(!n){t.textContent="Please enter some text to analyze.";return}r.disabled=!0,r.textContent="Analyzing...",t.innerHTML='<span class="loading">Analyzing confidence level...</span>';try{const e=await chrome.runtime.sendMessage({type:"ANALYZE_CONFIDENCE",payload:n});if(e.success&&e.analysis){const{confidence_score:s,explanation:d,needs_rewriting:a,suggested_improvements:l}=e.analysis,c=s>=7?"#10b981":s>=5?"#f59e0b":"#ef4444",y=s>=7?"✅":s>=5?"⚠️":"❌";t.innerHTML=`
                    <div style="margin-bottom: 12px;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <span style="font-size: 18px; margin-right: 8px;">${y}</span>
                            <strong style="color: ${c};">Confidence Score: ${s}/10</strong>
                        </div>
                        <div style="margin-bottom: 8px;"><strong>Analysis:</strong> ${d}</div>
                        ${a?`<div style="margin-bottom: 8px;"><strong>Suggestions:</strong> ${l}</div>`:""}
                    </div>
                    ${a?`<div style="padding: 12px; background: #fef3c7; border-radius: 6px; border-left: 4px solid #f59e0b;">
                            <strong>💡 Recommendation:</strong> This text could benefit from rewriting to sound more confident.
                        </div>`:`<div style="padding: 12px; background: #d1fae5; border-radius: 6px; border-left: 4px solid #10b981;">
                            <strong>👍 Great!</strong> This text already sounds confident and clear.
                        </div>`}
                `}else e.error?t.innerHTML=`<span style="color: #dc2626;">${e.error}</span>`:t.innerHTML='<span style="color: #dc2626;">Failed to analyze text. Please try again.</span>'}catch(e){console.error("Analysis Error:",e),t.innerHTML=`<span style="color: #dc2626;">Connection error: ${e.message}</span>`}finally{r.disabled=!1,r.textContent="Check Confidence"}}),i.addEventListener("keydown",function(n){(n.ctrlKey||n.metaKey)&&n.key==="Enter"&&r.click()})});
