document.addEventListener('DOMContentLoaded', function() {
    const inputText = document.getElementById('inputText');
    const rewriteBtn = document.getElementById('rewriteBtn');
    const output = document.getElementById('output');

    rewriteBtn.addEventListener('click', async function() {
        const text = inputText.value.trim();
        
        if (!text) {
            output.textContent = 'Please enter some text to rewrite.';
            return;
        }

        // Disable button and show loading state
        rewriteBtn.disabled = true;
        rewriteBtn.textContent = 'Rewriting...';
        output.innerHTML = '<span class="loading">Processing your text...</span>';

        try {
            // Send message to background script
            const response = await chrome.runtime.sendMessage({
                type: 'REWRITE_TEXT',
                payload: text
            });

            if (response && response.rewrittenText) {
                output.textContent = response.rewrittenText;
            } else {
                output.textContent = 'Sorry, there was an error rewriting your text. Please try again.';
            }
        } catch (error) {
            console.error('Error:', error);
            output.textContent = 'Sorry, there was an error rewriting your text. Please try again.';
        } finally {
            // Re-enable button
            rewriteBtn.disabled = false;
            rewriteBtn.textContent = 'Rewrite with Confidence';
        }
    });

    // Allow Enter key to trigger rewrite (with Ctrl/Cmd)
    inputText.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            rewriteBtn.click();
        }
    });
});
