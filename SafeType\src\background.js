async function rewriteWithConfidence(inputText) {
  try {
    // Get API key from Chrome storage
    const result = await chrome.storage.sync.get(['geminiApiKey']);
    const apiKey = result.geminiApiKey;

    console.log('API Key exists:', !!apiKey);

    if (!apiKey) {
      return 'Please set your Gemini API key in the extension options.';
    }

    if (!inputText || inputText.trim().length === 0) {
      return 'Please provide some text to rewrite.';
    }

    const body = {
      contents: [
        {
          parts: [
            {
              text: `Rewrite the following text in a more confident and calming tone. Keep the same meaning but make it sound more assured and positive:\n\n"${inputText}"`
            }
          ]
        }
      ]
    };

    console.log('Making API request to Gemini...');

    const res = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      }
    );

    console.log('Response status:', res.status);

    if (!res.ok) {
      const errorData = await res.json();
      console.error('API Error:', errorData);

      if (res.status === 400) {
        return 'Invalid API request. Please check your API key.';
      } else if (res.status === 403) {
        return 'API key is invalid or doesn\'t have permission. Please check your Gemini API key.';
      } else if (res.status === 429) {
        return 'Too many requests. Please wait a moment and try again.';
      } else {
        return `API Error (${res.status}): ${errorData.error?.message || 'Unknown error'}`;
      }
    }

    const data = await res.json();
    console.log('API Response:', data);

    if (data.error) {
      console.error('Gemini API Error:', data.error);
      return `Error: ${data.error.message}`;
    }

    const text = data?.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!text) {
      console.error('No text in response:', data);
      return 'No response generated. The content might have been filtered.';
    }

    return text.trim();

  } catch (error) {
    console.error('Rewrite function error:', error);
    return `Error: ${error.message}`;
  }
}

// Create context menu
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'rewriteText',
    title: 'Rewrite with SafeType',
    contexts: ['selection']
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'rewriteText' && info.selectionText) {
    rewriteWithConfidence(info.selectionText).then((result) => {
      // Inject script to replace selected text
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: replaceSelectedText,
        args: [result]
      });
    });
  }
});

function replaceSelectedText(newText) {
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    range.deleteContents();
    range.insertNode(document.createTextNode(newText));
    selection.removeAllRanges();
  }
}

chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.type === 'REWRITE_TEXT') {
    rewriteWithConfidence(msg.payload).then((result) => {
      sendResponse({ rewrittenText: result });
    });
    return true; // Keep message channel open for async
  }
});
