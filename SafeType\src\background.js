async function rewriteWithConfidence(inputText) {
  // Get API key from Chrome storage
  const result = await chrome.storage.sync.get(['geminiApiKey']);
  const apiKey = result.geminiApiKey;

  if (!apiKey) {
    return 'Please set your Gemini API key in the extension options.';
  }

  const body = {
    contents: [
      {
        parts: [
          {
            text: `Rewrite the following in a confident and calming tone:\n\n"${inputText}"`
          }
        ]
      }
    ]
  };

  const res = await fetch(
    `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    }
  );

  const data = await res.json();
  const text = data?.candidates?.[0]?.content?.parts?.[0]?.text || 'Rewrite failed';
  return text;
}

// Create context menu
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'rewriteText',
    title: 'Rewrite with SafeType',
    contexts: ['selection']
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'rewriteText' && info.selectionText) {
    rewriteWithConfidence(info.selectionText).then((result) => {
      // Inject script to replace selected text
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: replaceSelectedText,
        args: [result]
      });
    });
  }
});

function replaceSelectedText(newText) {
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    range.deleteContents();
    range.insertNode(document.createTextNode(newText));
    selection.removeAllRanges();
  }
}

chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.type === 'REWRITE_TEXT') {
    rewriteWithConfidence(msg.payload).then((result) => {
      sendResponse({ rewrittenText: result });
    });
    return true; // Keep message channel open for async
  }
});
