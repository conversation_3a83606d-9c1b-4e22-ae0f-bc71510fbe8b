import"./modulepreload-polyfill-B5Qt9EMX.js";document.addEventListener("DOMContentLoaded",function(){const o=document.getElementById("inputText"),t=document.getElementById("rewriteBtn"),n=document.getElementById("output");t.addEventListener("click",async function(){const r=o.value.trim();if(!r){n.textContent="Please enter some text to rewrite.";return}t.disabled=!0,t.textContent="Rewriting...",n.innerHTML='<span class="loading">Processing your text...</span>';try{const e=await chrome.runtime.sendMessage({type:"REWRITE_TEXT",payload:r});console.log("Response from background:",e),e&&e.rewrittenText?e.rewrittenText.startsWith("Error:")||e.rewrittenText.startsWith("Please set your")||e.rewrittenText.startsWith("API Error")||e.rewrittenText.startsWith("Invalid")||e.rewrittenText.startsWith("Too many")?n.innerHTML=`<span style="color: #dc2626;">${e.rewrittenText}</span>`:n.textContent=e.rewrittenText:n.innerHTML='<span style="color: #dc2626;">No response received. Please check your API key and try again.</span>'}catch(e){console.error("Popup Error:",e),n.innerHTML=`<span style="color: #dc2626;">Connection error: ${e.message}</span>`}finally{t.disabled=!1,t.textContent="Rewrite with Confidence"}}),o.addEventListener("keydown",function(r){(r.ctrlKey||r.metaKey)&&r.key==="Enter"&&t.click()})});
