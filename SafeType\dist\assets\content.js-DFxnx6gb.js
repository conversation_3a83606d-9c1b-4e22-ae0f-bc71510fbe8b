(function(){console.log("SafeType content script loaded");let d=null,s=null,r=null;function a(){["textarea",'input[type="text"]','input[type="email"]','[contenteditable="true"]','[role="textbox"]',".editable",'[data-testid*="compose"]','[data-testid*="message"]'].forEach(n=>{document.querySelectorAll(n).forEach(t=>{t.hasAttribute("data-safetype-monitored")||(t.setAttribute("data-safetype-monitored","true"),t.addEventListener("input",g),t.addEventListener("focus",u),t.addEventListener("blur",f))})})}function u(e){d=e.target}function f(e){d===e.target&&(d=null,o())}function g(e){const n=e.target,t=x(n);s&&clearTimeout(s),t.length>10?s=setTimeout(()=>{y(t,n)},2e3):o()}function x(e){return e.tagName==="TEXTAREA"||e.tagName==="INPUT"?e.value:e.contentEditable==="true"?e.innerText||e.textContent:""}async function y(e,n){try{const t=await chrome.runtime.sendMessage({type:"ANALYZE_CONFIDENCE",payload:e});if(t.success&&t.analysis){const{confidence_score:i,needs_rewriting:c,explanation:p,suggested_improvements:l}=t.analysis;i<6&&c?m(n,{score:i,explanation:p,improvements:l,originalText:e}):o()}}catch(t){console.error("SafeType: Error analyzing confidence:",t)}}document.addEventListener("mouseup",function(){const e=window.getSelection().toString().trim();e.length>0&&chrome.runtime.sendMessage({type:"TEXT_SELECTED",payload:e})});chrome.runtime.onMessage.addListener((e,n,t)=>{if(e.type==="GET_SELECTED_TEXT"){const i=window.getSelection().toString().trim();t({selectedText:i})}return!0});function m(e,n){o();const t=document.createElement("div");t.id="safetype-confidence-notification",t.innerHTML=`
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                <strong style="color: #d97706;">Low Confidence Detected</strong>
            </div>

            <div style="margin-bottom: 12px; color: #374151;">
                <div><strong>Confidence Score:</strong> ${n.score}/10</div>
                <div style="margin-top: 4px;"><strong>Issue:</strong> ${n.explanation}</div>
                <div style="margin-top: 4px;"><strong>Suggestion:</strong> ${n.improvements}</div>
            </div>

            <div style="margin-bottom: 12px; font-weight: 500; color: #374151;">
                Would you like me to rewrite this text with more confidence?
            </div>

            <div style="display: flex; gap: 8px;">
                <button id="safetype-rewrite-yes" style="
                    background: #4F46E5;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">Yes, Rewrite</button>

                <button id="safetype-rewrite-no" style="
                    background: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">No, Keep As Is</button>
            </div>
        </div>
    `,document.body.appendChild(t),r=t,document.getElementById("safetype-rewrite-yes").addEventListener("click",()=>{b(e,n.originalText)}),document.getElementById("safetype-rewrite-no").addEventListener("click",()=>{o()}),setTimeout(()=>{o()},15e3)}function o(){r&&(r.remove(),r=null)}async function b(e,n){const t=document.getElementById("safetype-rewrite-yes");t&&(t.textContent="Rewriting...",t.disabled=!0);try{const i=await chrome.runtime.sendMessage({type:"REWRITE_TEXT",payload:n});i&&i.rewrittenText&&(E(e,i.rewrittenText),r&&(r.innerHTML=`
                    <div style="
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #10b981;
                        color: white;
                        border-radius: 12px;
                        padding: 16px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        z-index: 10000;
                        max-width: 350px;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        font-size: 14px;
                        text-align: center;
                    ">
                        ✅ Text rewritten with more confidence!
                    </div>
                `,setTimeout(()=>{o()},3e3)))}catch(i){console.error("SafeType: Error rewriting text:",i),o()}}function E(e,n){e.tagName==="TEXTAREA"||e.tagName==="INPUT"?(e.value=n,e.dispatchEvent(new Event("input",{bubbles:!0}))):e.contentEditable==="true"&&(e.innerText=n,e.dispatchEvent(new Event("input",{bubbles:!0})))}a();const T=new MutationObserver(()=>{a()});T.observe(document.body,{childList:!0,subtree:!0});
})()
