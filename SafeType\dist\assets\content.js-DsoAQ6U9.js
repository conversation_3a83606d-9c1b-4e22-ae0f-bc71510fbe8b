(function(){console.log("SafeType content script loaded");let l=null,d=null,a=null;function f(){["textarea",'input[type="text"]','input[type="email"]','[contenteditable="true"]','[role="textbox"]',".editable",'[data-testid*="compose"]','[data-testid*="message"]','div[contenteditable="true"][role="textbox"]','div[contenteditable="true"][aria-label*="Message"]','div[contenteditable="true"][aria-label*="message"]','div[contenteditable="true"][aria-label*="compose"]','div[contenteditable="true"][aria-label*="reply"]','div[contenteditable="true"][aria-label*="forward"]','div[contenteditable="true"][data-tab]','div[contenteditable="true"][role="textbox"][spellcheck="true"]','div[contenteditable="true"][aria-label*="Tweet"]','div[contenteditable="true"][aria-label*="Post"]','div[contenteditable="true"][aria-label*="Write"]','div[contenteditable="true"][role="textbox"][aria-multiline="true"]','div[contenteditable="true"][role="textbox"][aria-label*="Message"]','div[contenteditable="true"][data-qa="message_input"]','div[contenteditable="true"][aria-label*="Write"]','div[contenteditable="true"][role="textbox"][aria-describedby]'].forEach(o=>{document.querySelectorAll(o).forEach(t=>{t.hasAttribute("data-safetype-monitored")||(t.setAttribute("data-safetype-monitored","true"),t.addEventListener("input",T),t.addEventListener("focus",y),t.addEventListener("blur",v))})})}function y(e){l=e.target}function v(e){l===e.target&&(l=null,r())}function T(e){const o=e.target,t=u(o);d&&clearTimeout(d),t.length>10?d=setTimeout(()=>{m(t,o)},2e3):r()}function u(e){return e.tagName==="TEXTAREA"||e.tagName==="INPUT"?e.value:e.contentEditable==="true"?e.innerText||e.textContent:""}async function m(e,o){try{const t=await chrome.runtime.sendMessage({type:"ANALYZE_CONFIDENCE",payload:e});if(t.success&&t.analysis){const{confidence_score:n,needs_rewriting:i,explanation:s,suggested_improvements:c}=t.analysis;n<6&&i?E(o,{score:n,explanation:s,improvements:c,originalText:e}):r()}}catch(t){console.error("SafeType: Error analyzing confidence:",t)}}document.addEventListener("mouseup",function(){const e=window.getSelection().toString().trim();e.length>0&&chrome.runtime.sendMessage({type:"TEXT_SELECTED",payload:e})});chrome.runtime.onMessage.addListener((e,o,t)=>{if(e.type==="GET_SELECTED_TEXT"){const n=window.getSelection().toString().trim();t({selectedText:n})}return!0});function E(e,o){r(),console.log("SafeType: Showing notification for element:",e);const t=document.createElement("div");t.id="safetype-confidence-notification",t.innerHTML=`
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                <strong style="color: #d97706;">Low Confidence Detected</strong>
            </div>

            <div style="margin-bottom: 12px; color: #374151;">
                <div><strong>Confidence Score:</strong> ${o.score}/10</div>
                <div style="margin-top: 4px;"><strong>Issue:</strong> ${o.explanation}</div>
                <div style="margin-top: 4px;"><strong>Suggestion:</strong> ${o.improvements}</div>
            </div>

            <div style="margin-bottom: 12px; font-weight: 500; color: #374151;">
                Would you like me to rewrite this text with more confidence?
            </div>

            <div style="display: flex; gap: 8px;">
                <button id="safetype-rewrite-yes" style="
                    background: #4F46E5;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">Yes, Rewrite</button>

                <button id="safetype-rewrite-no" style="
                    background: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">No, Keep As Is</button>
            </div>
        </div>
    `,document.body.appendChild(t),a=t,document.getElementById("safetype-rewrite-yes").addEventListener("click",()=>{w(e,o.originalText)}),document.getElementById("safetype-rewrite-no").addEventListener("click",()=>{r()}),setTimeout(()=>{r()},15e3)}function r(){a&&(a.remove(),a=null)}async function w(e,o){const t=document.getElementById("safetype-rewrite-yes");t&&(t.textContent="Rewriting...",t.disabled=!0);try{console.log("SafeType: Rewriting text for element:",e);const n=await chrome.runtime.sendMessage({type:"REWRITE_TEXT",payload:o});if(n&&n.rewrittenText)if(console.log("SafeType: Got rewritten text:",n.rewrittenText),e&&e.isConnected)p(e,n.rewrittenText),a&&(a.innerHTML=`
                        <div style="
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: #10b981;
                            color: white;
                            border-radius: 12px;
                            padding: 16px;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                            z-index: 10000;
                            max-width: 350px;
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            font-size: 14px;
                            text-align: center;
                        ">
                            ✅ Text rewritten with more confidence!
                        </div>
                    `,setTimeout(()=>{r()},3e3));else{console.error("SafeType: Element is no longer valid or connected");const i=document.activeElement;i&&(i.tagName==="TEXTAREA"||i.tagName==="INPUT"||i.contentEditable==="true")&&(console.log("SafeType: Using currently focused element instead"),p(i,n.rewrittenText))}else console.error("SafeType: No rewritten text received"),a&&(a.innerHTML=`
                    <div style="
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #ef4444;
                        color: white;
                        border-radius: 12px;
                        padding: 16px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        z-index: 10000;
                        max-width: 350px;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        font-size: 14px;
                        text-align: center;
                    ">
                        ❌ Failed to rewrite text. Please try again.
                    </div>
                `,setTimeout(()=>{r()},3e3))}catch(n){console.error("SafeType: Error rewriting text:",n),r()}}function p(e,o){try{console.log("SafeType: Attempting to set text in element:",e.tagName,e.className);const t=u(e);if(e.tagName==="TEXTAREA"||e.tagName==="INPUT")e.focus(),e.value=o,e.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),e.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0})),e.dispatchEvent(new KeyboardEvent("keyup",{bubbles:!0,cancelable:!0}));else if(e.contentEditable==="true"||e.getAttribute("contenteditable")==="true"){if(e.focus(),document.execCommand){const n=document.createRange();n.selectNodeContents(e);const i=window.getSelection();i.removeAllRanges(),i.addRange(n),document.execCommand("insertText",!1,o)}else e.innerText=o;e.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),e.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0})),e.dispatchEvent(new KeyboardEvent("keyup",{bubbles:!0,cancelable:!0}))}else{let n=e.parentElement;for(;n&&n!==document.body;){if(n.contentEditable==="true"||n.getAttribute("contenteditable")==="true"){p(n,o);return}n=n.parentElement}console.warn("SafeType: Could not determine how to set text for element:",e)}setTimeout(()=>{const n=u(e);n!==o?(console.warn("SafeType: Text replacement may have failed. Expected:",o,"Got:",n),g(e,o)):console.log("SafeType: Text successfully replaced")},100)}catch(t){console.error("SafeType: Error setting element text:",t),g(e,o)}}function g(e,o){try{console.log("SafeType: Trying alternative text replacement method"),e.focus(),navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(o).then(()=>{if(e.tagName==="TEXTAREA"||e.tagName==="INPUT")e.select();else{const t=document.createRange();t.selectNodeContents(e);const n=window.getSelection();n.removeAllRanges(),n.addRange(t)}document.execCommand("paste")}).catch(()=>{b(e,o)}):b(e,o)}catch(t){console.error("SafeType: Alternative text replacement failed:",t)}}function b(e,o){try{if(console.log("SafeType: Simulating typing"),e.focus(),e.tagName==="TEXTAREA"||e.tagName==="INPUT")e.select(),e.value="";else{const i=document.createRange();i.selectNodeContents(e);const s=window.getSelection();s.removeAllRanges(),s.addRange(i),s.deleteFromDocument()}let t=0;const n=()=>{if(t<o.length){const i=o[t],s=new KeyboardEvent("keydown",{key:i,char:i,bubbles:!0,cancelable:!0}),c=new KeyboardEvent("keypress",{key:i,char:i,bubbles:!0,cancelable:!0}),x=new InputEvent("input",{data:i,bubbles:!0,cancelable:!0});e.dispatchEvent(s),e.dispatchEvent(c),e.tagName==="TEXTAREA"||e.tagName==="INPUT"?e.value+=i:e.textContent+=i,e.dispatchEvent(x),t++,setTimeout(n,10)}};n()}catch(t){console.error("SafeType: Typing simulation failed:",t)}}f();const h=new MutationObserver(()=>{f()});h.observe(document.body,{childList:!0,subtree:!0});
})()
