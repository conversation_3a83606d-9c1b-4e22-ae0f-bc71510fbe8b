async function c(e){try{const r=(await chrome.storage.sync.get(["geminiApiKey"])).geminiApiKey;if(console.log("API Key exists:",!!r),!r)return"Please set your Gemini API key in the extension options.";if(!e||e.trim().length===0)return"Please provide some text to rewrite.";const s={contents:[{parts:[{text:`Rewrite the following text in a more confident and calming tone. Keep the same meaning but make it sound more assured and positive:

"${e}"`}]}]};console.log("Making API request to Gemini...");const n=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${r}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(console.log("Response status:",n.status),!n.ok){const a=await n.json();return console.error("API Error:",a),n.status===400?"Invalid API request. Please check your API key.":n.status===403?"API key is invalid or doesn't have permission. Please check your Gemini API key.":n.status===429?"Too many requests. Please wait a moment and try again.":`API Error (${n.status}): ${a.error?.message||"Unknown error"}`}const o=await n.json();if(console.log("API Response:",o),o.error)return console.error("Gemini API Error:",o.error),`Error: ${o.error.message}`;const i=o?.candidates?.[0]?.content?.parts?.[0]?.text;return i?i.trim():(console.error("No text in response:",o),"No response generated. The content might have been filtered.")}catch(t){return console.error("Rewrite function error:",t),`Error: ${t.message}`}}chrome.runtime.onInstalled.addListener(()=>{chrome.contextMenus.create({id:"rewriteText",title:"Rewrite with SafeType",contexts:["selection"]})});chrome.contextMenus.onClicked.addListener((e,t)=>{e.menuItemId==="rewriteText"&&e.selectionText&&c(e.selectionText).then(r=>{chrome.scripting.executeScript({target:{tabId:t.id},func:l,args:[r]})})});function l(e){const t=window.getSelection();if(t.rangeCount>0){const r=t.getRangeAt(0);r.deleteContents(),r.insertNode(document.createTextNode(e)),t.removeAllRanges()}}chrome.runtime.onMessage.addListener((e,t,r)=>{if(e.type==="REWRITE_TEXT")return c(e.payload).then(s=>{r({rewrittenText:s})}),!0});
